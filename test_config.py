#!/usr/bin/env python3
"""
测试YOLOv12 Stage1配置文件读取
检查scale参数是否被正确解析
"""

import yaml

# 直接用标准yaml库读取
cfg_path = 'ultralytics/cfg/models/v12/yolov12-stage1.yaml'
with open(cfg_path, 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

print("=== 标准YAML读取结果 ===")
print(f"nc: {config.get('nc')}")
print(f"ch: {config.get('ch')}")
print(f"scale: '{config.get('scale')}'")
print(f"scale type: {type(config.get('scale'))}")
print(f"scales: {config.get('scales')}")

# 再用ultralytics的yaml_model_load
from ultralytics.nn import yaml_model_load
config2 = yaml_model_load(cfg_path)

print(f"\n=== ultralytics yaml_model_load结果 ===")
print(f"nc: {config2.get('nc')}")
print(f"ch: {config2.get('ch')}")
print(f"scale: '{config2.get('scale')}'")
print(f"scale type: {type(config2.get('scale'))}")
print(f"scales: {config2.get('scales')}")

# 模拟parse_model中的scale选择逻辑
scales = config.get('scales')
if scales:
    scale = config.get('scale')
    print(f"\n=== Scale选择逻辑 ===")
    print(f"配置中的scale参数: '{scale}'")
    
    if not scale:
        scale = tuple(scales.keys())[0]
        print(f"WARNING ⚠️ no model scale passed. Assuming scale='{scale}'.")
    else:
        print(f"使用配置中指定的scale: '{scale}'")
    
    if scale in scales:
        depth, width, max_channels = scales[scale]
        print(f"选中的scale '{scale}' 参数: depth={depth}, width={width}, max_channels={max_channels}")
    else:
        print(f"ERROR: scale '{scale}' not found in scales")
else:
    print("No scales found in config")