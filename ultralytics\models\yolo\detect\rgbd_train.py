"""
YOLOv12 Stage1 双模态检测训练器
扩展标准DetectionTrainer以支持RGB+Depth数据
"""

from ultralytics.models.yolo.detect import DetectionTrainer
from ultralytics.data.rgbd_dataset import YOLORGBDDataset
from ultralytics.utils import yaml_load, colorstr


class RGBDDetectionTrainer(DetectionTrainer):
    """
    扩展DetectionTrainer以支持RGB+Depth双模态数据训练
    """

    def build_dataset(self, img_path, mode="train", batch=None):
        """
        构建RGB+Depth数据集
        
        Args:
            img_path (str): 图像路径
            mode (str): 模式 ('train', 'val')
            batch (int): 批次大小
            
        Returns:
            YOLORGBDDataset: RGB+Depth数据集实例
        """
        gs = max(int(de_parallel(self.model).stride.max() if self.model else 0), 32)
        
        # 从数据配置中获取depth_path
        data_config = yaml_load(self.args.data) if isinstance(self.args.data, str) else self.args.data
        depth_path = data_config.get('depth_path', None)
        
        if depth_path is None:
            raise ValueError("数据配置中缺少depth_path，请在YAML文件中指定深度图路径")
        
        return YOLORGBDDataset(
            img_path=img_path,
            imgsz=self.args.imgsz,
            batch_size=batch,
            augment=mode == "train",  # 只在训练时增强
            hyp=self.args,  # 使用self.args作为hyp参数
            rect=False if mode == "train" else self.args.rect,  # 训练时不使用rect
            cache=getattr(self.args, 'cache', False),
            single_cls=self.args.single_cls or False,
            stride=gs,
            pad=0.0 if mode == "train" else 0.5,
            prefix=colorstr(f"{mode}: "),
            task=self.args.task,
            classes=self.args.classes,
            data=data_config,
            fraction=self.args.fraction if mode == "train" else 1.0,
            # 自定义参数
            depth_path=depth_path,
        )


def de_parallel(model):
    """移除模型的DataParallel包装"""
    return model.module if hasattr(model, 'module') else model