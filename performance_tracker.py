# YOLOv12 消融实验性能跟踪系统
# 记录和对比各阶段的训练性能指标

import json
import csv
import os
import yaml
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd


class PerformanceTracker:
    """
    消融实验性能跟踪器
    记录各阶段训练结果，生成对比分析
    """
    
    def __init__(self, experiment_name="yolov12_ablation"):
        self.experiment_name = experiment_name
        self.results_file = f"results/{experiment_name}_results.json"
        self.csv_file = f"results/{experiment_name}_summary.csv"
        
        # 创建结果目录
        os.makedirs("results", exist_ok=True)
        
        # 初始化结果记录
        self.results = self._load_existing_results()
        
    def _load_existing_results(self):
        """加载已有的实验结果"""
        if os.path.exists(self.results_file):
            with open(self.results_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            "experiment_info": {
                "name": self.experiment_name,
                "created": datetime.now().isoformat(),
                "dataset": "KITTI",
                "classes": ["Car", "Pedestrian", "Cyclist"]
            },
            "stages": {}
        }
    
    def record_stage_result(self, stage_name, config_path, results_path, 
                           manual_metrics=None, training_time=None):
        """
        记录单个阶段的训练结果
        
        Args:
            stage_name (str): 阶段名称 (stage1, stage2, etc.)
            config_path (str): 模型配置文件路径
            results_path (str): 训练结果目录路径
            manual_metrics (dict): 手动输入的关键指标
            training_time (float): 训练耗时（分钟）
        """
        print(f"\n📊 记录阶段 {stage_name} 性能指标")
        print("=" * 50)
        
        # 自动解析训练结果
        auto_metrics = self._parse_training_results(results_path)
        
        # 合并自动和手动指标
        final_metrics = auto_metrics.copy()
        if manual_metrics:
            final_metrics.update(manual_metrics)
        
        # 记录完整信息
        stage_info = {
            "timestamp": datetime.now().isoformat(),
            "config_path": config_path,
            "results_path": results_path,
            "training_time_minutes": training_time,
            "model_parameters": self._get_model_parameters(config_path),
            "metrics": final_metrics,
            "status": "completed"
        }
        
        self.results["stages"][stage_name] = stage_info
        
        # 保存结果
        self._save_results()
        
        # 打印当前阶段结果
        self._print_stage_summary(stage_name, stage_info)
        
        return stage_info
    
    def _parse_training_results(self, results_path):
        """
        自动解析训练结果目录中的指标
        
        Args:
            results_path (str): 结果目录路径
            
        Returns:
            dict: 解析出的性能指标
        """
        metrics = {}
        
        try:
            # 查找results.csv文件
            csv_path = Path(results_path) / "results.csv"
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                # 获取最后一行的指标（最终epoch）
                last_row = df.iloc[-1]
                
                metrics.update({
                    "final_epoch": int(last_row.get("epoch", 0)),
                    "train_box_loss": float(last_row.get("train/box_loss", 0)),
                    "train_cls_loss": float(last_row.get("train/cls_loss", 0)),
                    "train_dfl_loss": float(last_row.get("train/dfl_loss", 0)),
                    "val_box_loss": float(last_row.get("val/box_loss", 0)),
                    "val_cls_loss": float(last_row.get("val/cls_loss", 0)),
                    "val_dfl_loss": float(last_row.get("val/dfl_loss", 0)),
                    "mAP50": float(last_row.get("metrics/mAP50(B)", 0)),
                    "mAP50_95": float(last_row.get("metrics/mAP50-95(B)", 0)),
                    "precision": float(last_row.get("metrics/precision(B)", 0)),
                    "recall": float(last_row.get("metrics/recall(B)", 0)),
                })
                
        except Exception as e:
            print(f"⚠️ 自动解析失败: {e}")
            
        return metrics
    
    def _get_model_parameters(self, config_path):
        """获取模型参数信息"""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return {
                "input_channels": config.get("ch", 4),
                "num_classes": config.get("nc", 3),
                "architecture": os.path.basename(config_path)
            }
        except:
            return {"architecture": os.path.basename(config_path)}
    
    def _print_stage_summary(self, stage_name, stage_info):
        """打印阶段结果摘要"""
        metrics = stage_info["metrics"]
        
        print(f"🎯 阶段 {stage_name} 训练完成")
        print(f"⏱️  训练时间: {stage_info.get('training_time_minutes', 'N/A')} 分钟")
        print(f"📈 最终指标:")
        print(f"   • mAP50: {metrics.get('mAP50', 0):.4f}")
        print(f"   • mAP50-95: {metrics.get('mAP50_95', 0):.4f}")
        print(f"   • Precision: {metrics.get('precision', 0):.4f}")
        print(f"   • Recall: {metrics.get('recall', 0):.4f}")
        print(f"💾 结果路径: {stage_info['results_path']}")
        
    def _save_results(self):
        """保存结果到文件"""
        # JSON格式（完整数据）
        with open(self.results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        # CSV格式（摘要对比）
        self._export_csv_summary()
    
    def _export_csv_summary(self):
        """导出CSV摘要对比表"""
        rows = []
        
        for stage_name, stage_info in self.results["stages"].items():
            metrics = stage_info["metrics"]
            row = {
                "Stage": stage_name,
                "Timestamp": stage_info["timestamp"][:19],  # 去掉毫秒
                "Training_Time_min": stage_info.get("training_time_minutes", "N/A"),
                "mAP50": f"{metrics.get('mAP50', 0):.4f}",
                "mAP50_95": f"{metrics.get('mAP50_95', 0):.4f}",
                "Precision": f"{metrics.get('precision', 0):.4f}",
                "Recall": f"{metrics.get('recall', 0):.4f}",
                "Box_Loss": f"{metrics.get('val_box_loss', 0):.4f}",
                "Cls_Loss": f"{metrics.get('val_cls_loss', 0):.4f}",
                "Architecture": stage_info.get("model_parameters", {}).get("architecture", "N/A")
            }
            rows.append(row)
        
        if rows:
            df = pd.DataFrame(rows)
            df.to_csv(self.csv_file, index=False)
            print(f"📊 摘要已导出: {self.csv_file}")
    
    def generate_comparison_report(self):
        """生成完整的对比分析报告"""
        if len(self.results["stages"]) < 2:
            print("⚠️ 需要至少2个阶段的结果才能生成对比报告")
            return
        
        print("\n" + "=" * 60)
        print("📊 YOLOv12 消融实验对比报告")
        print("=" * 60)
        
        # 打印摘要表格
        self._print_comparison_table()
        
        # 生成性能图表
        self._plot_performance_comparison()
        
        # 分析最佳性能
        self._analyze_best_performance()
    
    def _print_comparison_table(self):
        """打印对比表格"""
        if os.path.exists(self.csv_file):
            df = pd.read_csv(self.csv_file)
            print("\n📋 阶段性能对比:")
            print(df.to_string(index=False))
    
    def _plot_performance_comparison(self):
        """绘制性能对比图表"""
        stages = []
        map50_values = []
        map50_95_values = []
        
        for stage_name, stage_info in self.results["stages"].items():
            stages.append(stage_name)
            map50_values.append(stage_info["metrics"].get("mAP50", 0))
            map50_95_values.append(stage_info["metrics"].get("mAP50_95", 0))
        
        if len(stages) >= 2:
            plt.figure(figsize=(10, 6))
            
            x = range(len(stages))
            plt.plot(x, map50_values, 'bo-', label='mAP50', linewidth=2, markersize=8)
            plt.plot(x, map50_95_values, 'ro-', label='mAP50-95', linewidth=2, markersize=8)
            
            plt.xlabel('Stages')
            plt.ylabel('mAP')
            plt.title('YOLOv12 Ablation Study - Performance Comparison')
            plt.xticks(x, stages)
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            chart_path = f"results/{self.experiment_name}_performance.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            print(f"📈 性能图表已保存: {chart_path}")
    
    def _analyze_best_performance(self):
        """分析最佳性能阶段"""
        best_map50_stage = None
        best_map50_value = 0
        
        for stage_name, stage_info in self.results["stages"].items():
            map50 = stage_info["metrics"].get("mAP50", 0)
            if map50 > best_map50_value:
                best_map50_value = map50
                best_map50_stage = stage_name
        
        if best_map50_stage:
            print(f"\n🏆 最佳性能阶段: {best_map50_stage}")
            print(f"🎯 最高mAP50: {best_map50_value:.4f}")
    
    def wait_for_stage_completion(self, stage_name, results_path, 
                                 check_interval=60, max_wait_minutes=180):
        """
        等待阶段训练完成
        
        Args:
            stage_name (str): 阶段名称
            results_path (str): 结果目录路径
            check_interval (int): 检查间隔（秒）
            max_wait_minutes (int): 最大等待时间（分钟）
        """
        print(f"⏳ 等待阶段 {stage_name} 训练完成...")
        print(f"📁 监控路径: {results_path}")
        
        import time
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        
        while time.time() - start_time < max_wait_seconds:
            # 检查是否有best.pt文件（训练完成标志）
            best_model_path = Path(results_path) / "weights" / "best.pt"
            if best_model_path.exists():
                print(f"✅ 阶段 {stage_name} 训练完成!")
                return True
            
            time.sleep(check_interval)
            elapsed = (time.time() - start_time) / 60
            print(f"⏱️  已等待 {elapsed:.1f} 分钟...")
        
        print(f"⚠️ 等待超时 ({max_wait_minutes} 分钟)")
        return False


# 创建全局跟踪器实例
tracker = PerformanceTracker()


if __name__ == "__main__":
    print("🔬 YOLOv12 消融实验性能跟踪系统")
    print("=" * 50)
    
    # 示例：记录Stage1结果
    print("\n示例用法:")
    print("1. 训练完成后调用:")
    print("   tracker.record_stage_result('stage1', config_path, results_path)")
    print("2. 生成对比报告:")
    print("   tracker.generate_comparison_report()")
    
    print(f"\n📁 结果将保存到:")
    print(f"   JSON: {tracker.results_file}")
    print(f"   CSV:  {tracker.csv_file}")