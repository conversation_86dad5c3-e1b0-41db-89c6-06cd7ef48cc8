#!/usr/bin/env python3
"""
YOLOv12 简化训练脚本 - 快速消融实验专用
禁用可视化，专注核心性能测试
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from ultralytics.models.yolo.rgbd_model import YOLORGBD


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='YOLOv12 Fast Training')
    parser.add_argument('--data', type=str, default='datasets/kitti-stage1.yaml')
    parser.add_argument('--cfg', type=str, default='ultralytics/cfg/models/v12/yolov12-stage1.yaml')
    parser.add_argument('--epochs', type=int, default=20)
    parser.add_argument('--batch-size', type=int, default=16)
    parser.add_argument('--imgsz', type=int, default=640)
    parser.add_argument('--device', type=str, default='0')
    parser.add_argument('--workers', type=int, default=4)
    parser.add_argument('--project', type=str, default='runs/ablation_experiment')
    parser.add_argument('--name', type=str, default='fast_experiment')
    parser.add_argument('--save-period', type=int, default=10)
    
    return parser.parse_args()


def main():
    """快速训练主函数"""
    args = parse_args()
    
    print("🚀 YOLOv12 快速消融实验")
    print(f"📊 配置: {args.cfg}")
    print(f"🎯 实验: {args.name}")
    print("=" * 50)
    
    # 创建模型
    model = YOLORGBD(args.cfg)
    
    # 快速训练配置（禁用可视化）
    train_args = {
        'data': args.data,
        'imgsz': args.imgsz,
        'epochs': args.epochs,
        'batch': args.batch_size,
        'device': args.device,
        'workers': args.workers,
        'project': args.project,
        'name': args.name,
        'save_period': args.save_period,
        'verbose': True,
        'plots': False,      # 🔥 禁用可视化
        'show': False,       # 🔥 禁用显示
        'save_txt': False,   # 🔥 减少I/O
        'save_conf': False,  # 🔥 减少I/O
        'cache': False,      # 🔥 避免缓存问题
    }
    
    print("⚡ 开始快速训练（已禁用可视化）...")
    results = model.train(**train_args)
    
    # 提取关键指标
    try:
        final_map50 = results.results_dict.get('metrics/mAP50(B)', 0.0)
        final_map5095 = results.results_dict.get('metrics/mAP50-95(B)', 0.0)
        
        print("\n" + "=" * 50)
        print("🎉 训练完成！")
        print(f"📈 最终mAP50: {final_map50:.4f}")
        print(f"📈 最终mAP50-95: {final_map5095:.4f}")
        print(f"💾 模型路径: {model.trainer.best}")
        print("=" * 50)
        
        return {
            "mAP50": final_map50,
            "mAP50-95": final_map5095,
            "model_path": str(model.trainer.best)
        }
        
    except Exception as e:
        print(f"⚠️ 结果解析失败: {e}")
        return {"error": str(e)}


if __name__ == '__main__':
    main()