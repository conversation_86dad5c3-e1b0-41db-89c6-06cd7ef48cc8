#!/usr/bin/env python3
"""
YOLOv12 Stage1 服务器训练脚本
阶段1：基础双模态输入 + SimplifiedDepthExtractor

Usage:
    python train_stage1_server.py --data datasets/kitti-stage1-server.yaml --cfg ultralytics/cfg/models/v12/yolov12-stage1.yaml
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

# 使用我们自定义的RGBD YOLO模型
from ultralytics.models.yolo.rgbd_model import YOLORGBD


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='YOLOv12 Stage1 Server Training')
    parser.add_argument('--data', type=str, default='datasets/kitti-stage1-server.yaml', 
                       help='dataset YAML file')
    parser.add_argument('--cfg', type=str, default='ultralytics/cfg/models/v12/yolov12-stage1.yaml',
                       help='model configuration YAML file')
    parser.add_argument('--weights', type=str, default='', 
                       help='initial weights path (optional)')
    parser.add_argument('--imgsz', type=int, default=640, 
                       help='inference size (pixels)')
    parser.add_argument('--batch-size', type=int, default=16,  # 服务器可以用更大batch_size
                       help='batch size')
    parser.add_argument('--epochs', type=int, default=100,  # 服务器正式训练
                       help='number of epochs')
    parser.add_argument('--device', type=str, default='', 
                       help='device to run on, i.e. cuda device=0/1/2/3 or device=cpu')
    parser.add_argument('--workers', type=int, default=8, 
                       help='dataloader workers')
    parser.add_argument('--project', type=str, default='runs/train', 
                       help='project directory')
    parser.add_argument('--name', type=str, default='yolov12-stage1', 
                       help='experiment name')
    parser.add_argument('--save-period', type=int, default=10, 
                       help='save checkpoint every N epochs')
    parser.add_argument('--cache', action='store_true', 
                       help='cache images for faster training')
    parser.add_argument('--resume', type=str, default='', 
                       help='resume from checkpoint')
    
    return parser.parse_args()


def check_dataset_path(data_config_path):
    """检查数据集路径是否存在"""
    print("🔍 检查服务器数据集路径...")
    
    import yaml
    with open(data_config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    dataset_path = Path(config['path'])
    depth_path = Path(config['depth_path'])
    
    print(f"📁 数据集路径: {dataset_path}")
    print(f"📁 深度图路径: {depth_path}")
    
    if not dataset_path.exists():
        print(f"❌ 数据集路径不存在: {dataset_path}")
        print("请确保KITTI数据集在正确位置")
        return False
    
    if not depth_path.exists():
        print(f"❌ 深度图路径不存在: {depth_path}")
        print("请确保深度图在正确位置")
        return False
    
    # 检查训练集
    train_images = dataset_path / 'images' / 'train_set'
    train_labels = dataset_path / 'labels' / 'train_set'
    
    if not train_images.exists():
        print(f"❌ 训练图像目录不存在: {train_images}")
        return False
    
    if not train_labels.exists():
        print(f"❌ 训练标签目录不存在: {train_labels}")
        return False
    
    # 统计文件数量
    image_count = len(list(train_images.glob('*.jpg'))) + len(list(train_images.glob('*.png')))
    label_count = len(list(train_labels.glob('*.txt')))
    
    print(f"✅ 找到 {image_count} 个训练图像")
    print(f"✅ 找到 {label_count} 个训练标签")
    
    if image_count == 0:
        print("⚠️ 未找到训练图像，请检查数据集格式")
        return False
    
    return True


def main():
    """主训练函数"""
    args = parse_args()
    
    print("🚀 YOLOv12 Stage1 服务器训练")
    print("阶段1：基础双模态输入 + SimplifiedDepthExtractor")
    print("=" * 60)
    
    # 验证数据集路径
    if not check_dataset_path(args.data):
        print("\n❌ 数据集检查失败，请检查路径配置")
        return
    
    # 验证配置文件存在性
    if not os.path.exists(args.data):
        raise FileNotFoundError(f"Dataset YAML file not found: {args.data}")
    
    if not os.path.exists(args.cfg):
        raise FileNotFoundError(f"Model config file not found: {args.cfg}")
    
    # 打印配置信息
    print(f"📊 数据集配置: {args.data}")
    print(f"🏗️ 模型配置: {args.cfg}")
    print(f"🖼️ 图像大小: {args.imgsz}")
    print(f"📦 批次大小: {args.batch_size}")
    print(f"🔄 训练轮数: {args.epochs}")
    print(f"💻 设备: {args.device if args.device else 'auto'}")
    print(f"🎯 实验名称: {args.name}")
    print("-" * 60)
    
    try:
        # 创建YOLO模型 - 强制使用YOLOv12m规模
        if args.weights:
            print(f"📥 从预训练权重加载: {args.weights}")
            model = YOLORGBD(args.weights)
        else:
            print(f"🏗️ 从配置文件创建模型: {args.cfg}")
            model = YOLORGBD(args.cfg)
        
        # 训练参数
        train_args = {
            'data': args.data,
            'imgsz': args.imgsz,
            'epochs': args.epochs,
            'batch': args.batch_size,
            'device': args.device,
            'workers': args.workers,
            'project': args.project,
            'name': args.name,
            'save_period': args.save_period,
            'cache': args.cache,
            'verbose': True,
            'plots': False,  # 服务器可以开启可视化
        }
        
        if args.resume:
            train_args['resume'] = args.resume
            
        # 开始训练
        print("🔥 开始训练...")
        results = model.train(**train_args)
        
        print("=" * 60)
        print("🎉 训练完成!")
        print(f"📁 结果保存在: {args.project}/{args.name}")
        if hasattr(model, 'trainer'):
            print(f"🏆 最佳模型: {model.trainer.best}")
            print(f"📄 最后模型: {model.trainer.last}")
        print("=" * 60)
        
        return results
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == '__main__':
    print("💡 提示: 服务器训练可以使用更大的batch-size和epochs")
    print("💡 提示: 确保数据集在 /home/<USER>/kitti 路径下")
    print()
    
    main()