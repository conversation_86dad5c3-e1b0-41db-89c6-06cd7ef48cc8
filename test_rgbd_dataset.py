#!/usr/bin/env python3
"""
测试RGB+Depth数据集加载
"""

import sys
from pathlib import Path

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from ultralytics.data.rgbd_dataset import YOLORGBDDataset
from ultralytics.utils import yaml_load


def test_rgbd_dataset():
    """测试RGB+Depth数据集加载"""
    
    print("=" * 60)
    print("测试RGB+Depth数据集加载")
    print("=" * 60)
    
    try:
        # 加载数据配置
        data_config = yaml_load('datasets/kitti-stage1.yaml')
        print(f"数据配置: {data_config}")
        
        # 创建数据集
        print(f"\n创建训练数据集...")
        train_dataset = YOLORGBDDataset(
            img_path=data_config['path'] + '/' + data_config['train'],
            imgsz=640,
            batch_size=2,
            augment=False,  # 测试时不增强
            cache=False,
            single_cls=False,
            stride=32,
            pad=0.5,
            prefix='train: ',
            data=data_config,
            task='detect',
            fraction=0.1,  # 只使用10%的数据进行测试
            depth_path=data_config['depth_path'],
        )
        
        print(f"数据集创建成功!")
        print(f"图像数量: {len(train_dataset.im_files)}")
        print(f"深度图数量: {len([d for d in train_dataset.depth_files if d is not None]) if train_dataset.depth_files else 0}")
        
        # 测试加载一个样本
        print(f"\n测试加载第一个样本...")
        sample = train_dataset[0]
        
        print(f"样本键: {sample.keys()}")
        if 'img' in sample:
            img_shape = sample['img'].shape
            print(f"图像形状: {img_shape}")
            
            if img_shape[0] == 4:
                print("✅ 成功加载4通道图像 (RGB + Depth)!")
            else:
                print(f"❌ 图像通道数错误: {img_shape[0]}, 期望4通道")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_rgbd_dataset()
    
    if success:
        print("\n🎉 RGB+Depth数据集测试通过!")
    else:
        print("\n⚠️ 数据集测试失败，请检查配置")