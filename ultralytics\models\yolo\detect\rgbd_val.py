"""
YOLOv12 Stage1 RGB+D检测验证器
扩展标准DetectionValidator以支持4通道输入
"""

import torch
from ultralytics.models.yolo.detect import DetectionValidator
from ultralytics.data.rgbd_dataset import YOLORGBDDataset
from ultralytics.utils import yaml_load, colorstr


class RGBDDetectionValidator(DetectionValidator):
    """
    扩展DetectionValidator以支持RGB+Depth双模态数据验证
    """

    def build_dataset(self, img_path, mode="val", batch=None):
        """
        构建RGB+Depth验证数据集
        
        Args:
            img_path (str): 图像路径
            mode (str): 模式 ('val')
            batch (int): 批次大小
            
        Returns:
            YOLORGBDDataset: RGB+Depth数据集实例
        """
        gs = max(int(de_parallel(self.model).stride.max() if self.model else 0), 32)
        
        # 从数据配置中获取depth_path
        data_config = yaml_load(self.args.data) if isinstance(self.args.data, str) else self.args.data
        depth_path = data_config.get('depth_path', None)
        
        if depth_path is None:
            raise ValueError("数据配置中缺少depth_path，请在YAML文件中指定深度图路径")
        
        return YOLORGBDDataset(
            img_path=img_path,
            imgsz=self.args.imgsz,
            batch_size=batch,
            augment=False,  # 验证时不增强
            hyp=self.args,
            rect=True,  # 验证时使用rect
            cache=getattr(self.args, 'cache', False),
            single_cls=self.args.single_cls or False,
            stride=gs,
            pad=0.5,
            prefix=colorstr(f"{mode}: "),
            task=self.args.task,
            classes=self.args.classes,
            data=data_config,
            fraction=1.0,  # 验证时使用全部数据
            # 自定义参数
            depth_path=depth_path,
        )

    def warmup_model(self, model, imgsz=(640, 640)):
        """
        为RGB+D模型执行warmup（使用4通道输入）
        
        Args:
            model: 模型实例
            imgsz: 图像尺寸
        """
        if hasattr(model, 'warmup'):
            # 创建4通道虚拟输入进行warmup
            dummy_input = torch.zeros(1, 4, imgsz[0], imgsz[1], device=next(model.parameters()).device)
            model.warmup(dummy_input)
        else:
            # 如果没有warmup方法，执行一次前向传播
            dummy_input = torch.zeros(1, 4, imgsz[0], imgsz[1], device=next(model.parameters()).device)
            with torch.no_grad():
                model(dummy_input)

    def __call__(self, trainer=None, model=None):
        """
        重写验证调用，避免3通道warmup问题
        """
        # 临时禁用model.warmup以避免3通道问题
        original_warmup = None
        if model and hasattr(model, 'warmup'):
            original_warmup = model.warmup
            model.warmup = self._rgbd_warmup_wrapper(model)
        
        try:
            # 调用父类验证逻辑
            return super().__call__(trainer, model)
        finally:
            # 恢复原始warmup
            if model and original_warmup:
                model.warmup = original_warmup
    
    def _rgbd_warmup_wrapper(self, model):
        """
        创建4通道warmup包装器
        """
        def rgbd_warmup(imgsz=(1, 3, 640, 640)):
            # 将3通道改为4通道
            if len(imgsz) == 4 and imgsz[1] == 3:
                imgsz = (imgsz[0], 4, imgsz[2], imgsz[3])
            elif len(imgsz) == 3 and imgsz[0] == 3:
                imgsz = (4, imgsz[1], imgsz[2])
            
            # 创建4通道输入
            device = next(model.parameters()).device
            dummy_input = torch.zeros(*imgsz, device=device)
            
            # 执行warmup
            with torch.no_grad():
                model(dummy_input)
        
        return rgbd_warmup


def de_parallel(model):
    """移除模型的DataParallel包装"""
    return model.module if hasattr(model, 'module') else model