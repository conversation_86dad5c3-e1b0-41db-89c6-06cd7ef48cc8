# Stage1 RGB+Depth Dataset Extension
# 扩展YOLODataset以支持RGB+Depth双模态输入

import os
import cv2
import numpy as np
import torch
from pathlib import Path
from copy import deepcopy
from ultralytics.data.dataset import YOLODataset
from ultralytics.data.rgbd_augment import RGBDTransforms, RGBDVisualization


class YOLORGBDDataset(YOLODataset):
    """
    阶段1：RGB+Depth双模态数据集
    扩展YOLODataset以支持RGB图像和深度图像的同时加载
    
    预期数据格式:
    dataset/
    ├── images/
    │   ├── train/
    │   │   ├── image1.jpg (RGB图像)
    │   │   └── image2.jpg
    │   └── val/
    └── depth/
        ├── train/
        │   ├── image1.png (对应的深度图，单通道)
        │   └── image2.png
        └── val/
    """

    def __init__(self, *args, depth_path=None, data=None, task="detect", **kwargs):
        """
        初始化RGB+Depth数据集
        
        Args:
            depth_path (str): 深度图像根目录路径
            data (dict): 数据集配置字典
            task (str): 任务类型，默认"detect"
            *args, **kwargs: 传递给父类YOLODataset的参数
        """
        # 保存深度路径
        self.depth_path = depth_path
        
        # 初始化父类 
        super().__init__(*args, data=data, task=task, **kwargs)
        
        # 验证深度路径
        if depth_path and not os.path.exists(depth_path):
            print(f"警告: 深度图路径不存在: {depth_path}")
            
        # 为每个图像文件找到对应的深度文件
        self.depth_files = self._get_depth_files() if depth_path else None

    def _get_depth_files(self):
        """
        为每个RGB图像找到对应的深度图像文件
        适配KITTI数据集结构
        
        Returns:
            list: 深度图像文件路径列表，与self.im_files对应
        """
        depth_files = []
        
        for img_file in self.im_files:
            # KITTI结构: /path/kitti/images/train_set/xxx.jpg -> /path/kitti/depths/train_set/xxx.png
            img_path = Path(img_file)
            
            # 获取相对于images目录的路径
            # 例如: images/train_set/000001.jpg -> train_set/000001.jpg
            try:
                # 找到images在路径中的位置
                parts = img_path.parts
                images_idx = None
                for i, part in enumerate(parts):
                    if part == 'images':
                        images_idx = i
                        break
                
                if images_idx is not None:
                    # 构建depths路径
                    relative_path = Path(*parts[images_idx + 1:])  # train_set/xxx.jpg
                    # 将.jpg改为.png (深度图通常是PNG格式)
                    depth_relative = relative_path.with_suffix('.png')
                    depth_file = Path(self.depth_path) / depth_relative
                else:
                    # 如果找不到images目录，尝试直接替换
                    depth_file = Path(str(img_file).replace('/images/', '/depths/')).with_suffix('.png')
                
                if depth_file.exists():
                    depth_files.append(str(depth_file))
                else:
                    # 如果找不到深度图，使用None占位
                    depth_files.append(None)
                    if len(depth_files) <= 5:  # 只打印前几个警告
                        print(f"警告: 未找到深度图 {depth_file}")
                        
            except Exception as e:
                print(f"处理深度图路径时出错 {img_file}: {e}")
                depth_files.append(None)
        
        found_depth = sum(1 for d in depth_files if d is not None)
        print(f"找到 {found_depth}/{len(depth_files)} 个深度图像")
        
        return depth_files

    def validate_depth_data(self, depth_img, img_path=""):
        """
        验证深度图数据质量

        Args:
            depth_img (np.ndarray): 深度图像
            img_path (str): 图像路径（用于调试）

        Returns:
            bool: 数据是否有效
        """
        if depth_img is None:
            return False

        # 检查数据范围
        min_val, max_val = depth_img.min(), depth_img.max()

        # 检查非零像素比例
        non_zero_ratio = np.count_nonzero(depth_img) / depth_img.size

        # 数据质量检查
        if non_zero_ratio < 0.5:  # 非零像素少于50%
            print(f"警告: 深度图 {img_path} 非零像素比例过低: {non_zero_ratio:.2%}")

        if max_val == min_val:  # 所有像素值相同
            print(f"警告: 深度图 {img_path} 缺乏深度变化")
            return False

        # 打印数据统计（仅前几张图）
        if len(self.depth_files) <= 5:
            print(f"深度图 {img_path}: 范围[{min_val}, {max_val}], "
                  f"非零比例{non_zero_ratio:.2%}, 均值{depth_img.mean():.1f}")

        return True

    def load_image(self, i, rect_mode=False):
        """
        加载RGB图像和对应的深度图像
        
        Args:
            i (int): 图像索引
            rect_mode (bool): 是否使用矩形模式
            
        Returns:
            tuple: (combined_image, hw_original, hw_resized) 
                   combined_image: 4通道图像 [H, W, 4] (RGB + Depth)
                   hw_original: 原始图像尺寸
                   hw_resized: 调整后的图像尺寸
        """
        # 加载RGB图像 - BaseDataset.load_image()返回(im, hw_original, hw_resized)
        rgb_img, hw_original, hw_resized = super().load_image(i, rect_mode)
        
        # 加载深度图像
        if self.depth_files and self.depth_files[i]:
            depth_img = cv2.imread(self.depth_files[i], cv2.IMREAD_GRAYSCALE)

            if depth_img is not None:
                # 验证深度图数据质量
                if not self.validate_depth_data(depth_img, self.depth_files[i]):
                    print(f"深度图质量检查失败: {self.depth_files[i]}")

                # 调整深度图尺寸与RGB图像匹配
                if depth_img.shape[:2] != rgb_img.shape[:2]:
                    depth_img = cv2.resize(depth_img, (rgb_img.shape[1], rgb_img.shape[0]))

                # 深度图预处理 - 针对已经是0-255范围的数据优化
                if depth_img.dtype != np.uint8:
                    depth_img = depth_img.astype(np.uint8)

                # 检查数据范围，如果超出0-255则进行归一化
                if depth_img.max() > 255:
                    # 对于超出范围的数据，使用归一化
                    depth_img = (depth_img / depth_img.max() * 255).astype(np.uint8)
                elif depth_img.max() <= 1.0:
                    # 如果数据在0-1范围，转换到0-255
                    depth_img = (depth_img * 255).astype(np.uint8)

                # 可选：轻微的深度图增强（提高对比度）
                # depth_img = cv2.equalizeHist(depth_img)  # 直方图均衡化

                # 可选：深度图平滑（减少噪声）
                # depth_img = cv2.medianBlur(depth_img, 3)  # 中值滤波

                # 扩展深度图维度 [H, W] -> [H, W, 1]
                depth_img = np.expand_dims(depth_img, axis=2)
            else:
                # 如果深度图加载失败，创建零深度图
                depth_img = np.zeros((rgb_img.shape[0], rgb_img.shape[1], 1), dtype=np.uint8)
        else:
            # 如果没有深度图，创建零深度图
            depth_img = np.zeros((rgb_img.shape[0], rgb_img.shape[1], 1), dtype=np.uint8)
        
        # 合并RGB和Depth: [H, W, 3] + [H, W, 1] = [H, W, 4]
        combined_img = np.concatenate([rgb_img, depth_img], axis=2)
        
        return combined_img, hw_original, hw_resized

    def __getitem__(self, index):
        """
        获取数据样本
        
        Args:
            index (int): 样本索引
            
        Returns:
            dict: 包含4通道图像和标签的字典
        """
        return self.transforms(self.get_image_and_label(index))

    def get_image_and_label(self, index):
        """
        获取图像和标签数据
        
        Args:
            index (int): 样本索引
            
        Returns:
            dict: 包含图像和标签信息的字典
        """
        label = deepcopy(self.labels[index])  # 深拷贝标签以避免修改原始数据
        label["img"], label["ori_shape"], label["resized_shape"] = self.load_image(index)
        label["ratio_pad"] = (
            label["resized_shape"][0] / label["ori_shape"][0],
            label["resized_shape"][1] / label["ori_shape"][1],
        )  # for evaluation
        
        if self.rect:
            label["rect_shape"] = self.batch_shapes[self.batch[index]]
        
        return self.update_labels_info(label)

    def build_transforms(self, hyp=None):
        """
        构建RGB+D专用的数据增强管道
        
        Args:
            hyp: 超参数配置
            
        Returns:
            RGBDTransforms: 自定义的4通道数据增强管道
        """
        return RGBDTransforms(
            imgsz=self.imgsz,
            augment=self.augment,
            hyp=hyp
        )

    @staticmethod
    def collate_fn(batch):
        """
        批处理函数，将多个样本合并为一个批次
        
        Args:
            batch (list): 样本列表
            
        Returns:
            dict: 批处理后的数据字典
        """
        new_batch = {}
        keys = batch[0].keys()
        values = list(zip(*[list(b.values()) for b in batch]))
        
        for i, k in enumerate(keys):
            value = values[i]
            if k == "img":
                # 确保图像是4通道的
                img_batch = []
                for img in value:
                    if img.shape[0] == 4:  # 已经是4通道
                        img_batch.append(img)
                    elif img.shape[0] == 3:  # 只有RGB，需要添加深度通道
                        depth_channel = torch.zeros(1, img.shape[1], img.shape[2], dtype=img.dtype, device=img.device)
                        img_4ch = torch.cat([img, depth_channel], dim=0)
                        img_batch.append(img_4ch)
                    else:
                        raise ValueError(f"Unexpected image channels: {img.shape[0]}")
                
                new_batch[k] = torch.stack(img_batch, 0)
            elif k in {"masks", "keypoints", "bboxes", "cls", "segments", "obb"}:
                new_batch[k] = torch.cat(value, 0)
            else:
                new_batch[k] = value
        
        # 处理批次索引
        new_batch["batch_idx"] = list(new_batch["batch_idx"])
        for i in range(len(new_batch["batch_idx"])):
            new_batch["batch_idx"][i] += i
        new_batch["batch_idx"] = torch.cat(new_batch["batch_idx"], 0)
        
        return new_batch