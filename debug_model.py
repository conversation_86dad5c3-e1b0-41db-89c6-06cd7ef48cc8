#!/usr/bin/env python3
"""
调试YOLOv12 Stage1模型构建过程
"""

import torch
from ultralytics.nn.tasks import DetectionModel

def debug_model_build():
    """调试模型构建过程"""
    
    try:
        print("开始构建模型...")
        cfg_path = "ultralytics/cfg/models/v12/yolov12-stage1.yaml"
        
        # 创建模型，输入4通道
        model = DetectionModel(cfg=cfg_path, ch=4, nc=80, verbose=True)
        
        print("\n模型构建成功!")
        print(f"总参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        print("\n测试前向传播...")
        test_input = torch.randn(1, 4, 320, 320)  # 较小的输入尺寸
        
        model.eval()
        with torch.no_grad():
            output = model(test_input)
            print(f"前向传播成功!")
            
            # 处理不同类型的输出
            if isinstance(output, (list, tuple)):
                print(f"输出是列表/元组，包含 {len(output)} 个元素:")
                for i, o in enumerate(output):
                    if hasattr(o, 'shape'):
                        print(f"  输出 {i}: 形状 {o.shape}")
                    elif isinstance(o, (list, tuple)):
                        print(f"  输出 {i}: 嵌套列表，长度 {len(o)}")
                        for j, sub_o in enumerate(o):
                            if hasattr(sub_o, 'shape'):
                                print(f"    子输出 {j}: 形状 {sub_o.shape}")
                    else:
                        print(f"  输出 {i}: 类型 {type(o)}")
            else:
                print(f"输出形状: {output.shape}")
            
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_model_build()