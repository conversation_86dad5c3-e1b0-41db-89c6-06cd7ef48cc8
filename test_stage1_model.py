#!/usr/bin/env python3
"""
YOLOv12 Stage1 模型测试脚本
用于验证阶段1模型配置是否正确

Usage:
    python test_stage1_model.py
"""

import torch
import numpy as np
from ultralytics.nn.tasks import DetectionModel


def test_stage1_model():
    """测试阶段1模型是否能正确构建和前向传播"""
    
    print("=" * 60)
    print("YOLOv12 Stage1 模型测试")
    print("=" * 60)
    
    try:
        # 模型配置路径
        cfg_path = "ultralytics/cfg/models/v12/yolov12-stage1.yaml"
        
        print(f"加载模型配置: {cfg_path}")
        
        # 创建模型（4通道输入）
        model = DetectionModel(cfg=cfg_path, ch=4, nc=80)
        
        print(f"模型创建成功!")
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
        
        # 创建测试输入 (4通道: RGB + Depth)
        batch_size = 2
        height, width = 640, 640
        test_input = torch.randn(batch_size, 4, height, width)
        
        print(f"测试输入形状: {test_input.shape}")
        
        # 设置为评估模式
        model.eval()
        
        print("执行前向传播...")
        
        with torch.no_grad():
            # 前向传播
            outputs = model(test_input)
            
            print("前向传播成功!")
            
            if isinstance(outputs, (list, tuple)):
                print(f"输出数量: {len(outputs)}")
                for i, output in enumerate(outputs):
                    print(f"输出 {i} 形状: {output.shape}")
            else:
                print(f"输出形状: {outputs.shape}")
        
        print("\n" + "=" * 60)
        print("✅ 阶段1模型测试通过!")
        print("模型可以正确处理4通道输入（RGB + Depth）")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 模型测试失败: {e}")
        print("请检查模型配置和相关模块实现")
        import traceback
        traceback.print_exc()
        return False


def test_input_separator():
    """单独测试InputSeparator模块"""
    
    print("\n" + "-" * 40)
    print("测试InputSeparator模块")
    print("-" * 40)
    
    try:
        from ultralytics.nn.modules.block import InputSeparator
        
        separator = InputSeparator()
        
        # 创建4通道测试输入
        test_input = torch.randn(2, 4, 32, 32)
        print(f"输入形状: {test_input.shape}")
        
        # 前向传播
        rgb_output = separator(test_input)
        depth_output = separator.get_depth_output()
        
        print(f"RGB输出形状: {rgb_output.shape}")
        print(f"Depth输出形状: {depth_output.shape}")
        
        # 验证分离是否正确
        assert rgb_output.shape == (2, 3, 32, 32), f"RGB形状错误: {rgb_output.shape}"
        assert depth_output.shape == (2, 1, 32, 32), f"Depth形状错误: {depth_output.shape}"
        
        # 验证数据是否正确分离
        assert torch.allclose(rgb_output, test_input[:, :3, :, :]), "RGB数据分离错误"
        assert torch.allclose(depth_output, test_input[:, 3:4, :, :]), "Depth数据分离错误"
        
        print("✅ InputSeparator测试通过!")
        
        return True
        
    except Exception as e:
        print(f"❌ InputSeparator测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_depth_conv():
    """测试DepthConv模块"""
    
    print("\n" + "-" * 40)
    print("测试DepthConv模块")
    print("-" * 40)
    
    try:
        from ultralytics.nn.modules.block import DepthConv
        
        # 创建DepthConv
        depth_conv = DepthConv(c1=1, c2=32, k=3, s=2)
        
        # 创建测试输入（单通道深度图）
        test_input = torch.randn(2, 1, 64, 64)
        print(f"输入形状: {test_input.shape}")
        
        # 前向传播
        output = depth_conv(test_input)
        print(f"输出形状: {output.shape}")
        
        # 验证输出形状
        expected_shape = (2, 32, 32, 32)  # stride=2, so 64/2=32
        assert output.shape == expected_shape, f"输出形状错误: {output.shape}, 期望: {expected_shape}"
        
        print("✅ DepthConv测试通过!")
        
        return True
        
    except Exception as e:
        print(f"❌ DepthConv测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    # 运行所有测试
    tests_passed = 0
    total_tests = 3
    
    if test_input_separator():
        tests_passed += 1
        
    if test_depth_conv():
        tests_passed += 1
        
    if test_stage1_model():
        tests_passed += 1
    
    print(f"\n🏁 测试完成: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！阶段1实现准备就绪")
    else:
        print("⚠️  部分测试失败，请修复后重试")