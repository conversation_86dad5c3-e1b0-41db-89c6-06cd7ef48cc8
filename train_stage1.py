#!/usr/bin/env python3
"""
YOLOv12 Stage1 训练脚本
阶段1：基础双模态输入 + SimplifiedDepthExtractor

Usage:
    python train_stage1.py --data datasets/coco-stage1.yaml --cfg ultralytics/cfg/models/v12/yolov12-stage1.yaml
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

# 使用我们自定义的RGBD YOLO模型
from ultralytics.models.yolo.rgbd_model import YOLORGBD


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='YOLOv12 Stage1 Training')
    parser.add_argument('--data', type=str, default='datasets/coco-stage1.yaml', 
                       help='dataset YAML file')
    parser.add_argument('--cfg', type=str, default='ultralytics/cfg/models/v12/yolov12-stage1.yaml',
                       help='model configuration YAML file')
    parser.add_argument('--weights', type=str, default='', 
                       help='initial weights path (optional)')
    parser.add_argument('--imgsz', type=int, default=640, 
                       help='inference size (pixels)')
    parser.add_argument('--batch-size', type=int, default=16, 
                       help='batch size')
    parser.add_argument('--epochs', type=int, default=100, 
                       help='number of epochs')
    parser.add_argument('--device', type=str, default='', 
                       help='device to run on, i.e. cuda device=0/1/2/3 or device=cpu')
    parser.add_argument('--workers', type=int, default=8, 
                       help='dataloader workers')
    parser.add_argument('--project', type=str, default='runs/train', 
                       help='project directory')
    parser.add_argument('--name', type=str, default='yolov12-stage1', 
                       help='experiment name')
    parser.add_argument('--save-period', type=int, default=10, 
                       help='save checkpoint every N epochs')
    parser.add_argument('--cache', action='store_true', 
                       help='cache images for faster training')
    parser.add_argument('--resume', type=str, default='', 
                       help='resume from checkpoint')
    parser.add_argument('--depth-path', type=str, default='', 
                       help='path to depth images directory')
    
    return parser.parse_args()


def main():
    """主训练函数"""
    args = parse_args()
    
    print("=" * 60)
    print("YOLOv12 Stage1 训练开始")
    print("阶段1：基础双模态输入 + SimplifiedDepthExtractor")
    print("=" * 60)
    
    # 验证文件存在性
    if not os.path.exists(args.data):
        raise FileNotFoundError(f"Dataset YAML file not found: {args.data}")
    
    if not os.path.exists(args.cfg):
        raise FileNotFoundError(f"Model config file not found: {args.cfg}")
    
    # 打印配置信息
    print(f"数据集配置: {args.data}")
    print(f"模型配置: {args.cfg}")
    print(f"图像大小: {args.imgsz}")
    print(f"批次大小: {args.batch_size}")
    print(f"训练轮数: {args.epochs}")
    print(f"设备: {args.device if args.device else 'auto'}")
    
    if args.depth_path:
        print(f"深度图路径: {args.depth_path}")
        
    print("-" * 60)
    
    try:
        # 创建YOLO模型
        if args.weights:
            print(f"从预训练权重加载: {args.weights}")
            model = YOLORGBD(args.weights)
        else:
            print(f"从配置文件创建模型: {args.cfg}")
            model = YOLORGBD(args.cfg)
        
        # 训练参数
        train_args = {
            'data': args.data,
            'imgsz': args.imgsz,
            'epochs': args.epochs,
            'batch': args.batch_size,
            'device': args.device,
            'workers': args.workers,
            'project': args.project,
            'name': args.name,
            'save_period': args.save_period,
            'cache': args.cache,
            'verbose': True,
        }
        
        if args.resume:
            train_args['resume'] = args.resume
            
        # 开始训练
        print("开始训练...")
        results = model.train(**train_args)
        
        print("=" * 60)
        print("训练完成!")
        print(f"最佳模型保存在: {model.trainer.best}")
        print(f"最后模型保存在: {model.trainer.last}")
        print("=" * 60)
        
        return results
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        raise


if __name__ == '__main__':
    main()