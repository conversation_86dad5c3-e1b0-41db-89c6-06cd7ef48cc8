#!/usr/bin/env python3
"""
深度图预处理分析工具
分析深度图数据质量和预处理效果
"""

import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from ultralytics.data.rgbd_dataset import YOLORGBDDataset
from ultralytics.utils import yaml_load


def analyze_depth_preprocessing(data_config_path, num_samples=10):
    """
    分析深度图预处理效果
    
    Args:
        data_config_path (str): 数据配置文件路径
        num_samples (int): 分析的样本数量
    """
    print("=" * 80)
    print("深度图预处理分析")
    print("=" * 80)
    
    # 加载数据配置
    data_config = yaml_load(data_config_path)
    print(f"数据配置: {data_config_path}")
    
    # 创建数据集
    dataset = YOLORGBDDataset(
        img_path=data_config['path'] + '/' + data_config['train'],
        imgsz=640,
        batch_size=1,
        augment=False,
        cache=False,
        single_cls=False,
        stride=32,
        pad=0.5,
        prefix='analyze: ',
        data=data_config,
        task='detect',
        fraction=0.1,
        depth_path=data_config['depth_path'],
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 分析样本
    depth_stats = []
    
    for i in range(min(num_samples, len(dataset))):
        try:
            # 加载样本
            sample = dataset[i]
            
            if 'img' in sample and sample['img'].shape[0] == 4:
                # 提取深度通道 (CHW格式)
                depth_channel = sample['img'][3].numpy()  # 第4通道
                
                # 统计信息
                stats = {
                    'index': i,
                    'shape': depth_channel.shape,
                    'dtype': depth_channel.dtype,
                    'min': depth_channel.min(),
                    'max': depth_channel.max(),
                    'mean': depth_channel.mean(),
                    'std': depth_channel.std(),
                    'non_zero_ratio': np.count_nonzero(depth_channel) / depth_channel.size,
                    'percentiles': np.percentile(depth_channel[depth_channel > 0], [25, 50, 75, 95]) if np.any(depth_channel > 0) else [0, 0, 0, 0]
                }
                
                depth_stats.append(stats)
                
                print(f"\n[{i+1}/{num_samples}] 样本 {i}:")
                print(f"  📐 深度通道形状: {stats['shape']}")
                print(f"  🔢 数据类型: {stats['dtype']}")
                print(f"  📊 数值范围: [{stats['min']:.3f}, {stats['max']:.3f}]")
                print(f"  📈 均值: {stats['mean']:.3f}")
                print(f"  📉 标准差: {stats['std']:.3f}")
                print(f"  🎯 非零像素比例: {stats['non_zero_ratio']:.2%}")
                print(f"  📊 百分位数: P25={stats['percentiles'][0]:.1f}, P50={stats['percentiles'][1]:.1f}, "
                      f"P75={stats['percentiles'][2]:.1f}, P95={stats['percentiles'][3]:.1f}")
                
        except Exception as e:
            print(f"❌ 分析样本 {i} 失败: {e}")
    
    # 汇总统计
    if depth_stats:
        print("\n" + "=" * 80)
        print("汇总统计")
        print("=" * 80)
        
        all_mins = [s['min'] for s in depth_stats]
        all_maxs = [s['max'] for s in depth_stats]
        all_means = [s['mean'] for s in depth_stats]
        all_stds = [s['std'] for s in depth_stats]
        all_ratios = [s['non_zero_ratio'] for s in depth_stats]
        
        print(f"📊 全局最小值: {min(all_mins):.3f}")
        print(f"📊 全局最大值: {max(all_maxs):.3f}")
        print(f"📊 均值范围: [{min(all_means):.3f}, {max(all_means):.3f}]")
        print(f"📊 标准差范围: [{min(all_stds):.3f}, {max(all_stds):.3f}]")
        print(f"📊 非零像素比例范围: [{min(all_ratios):.2%}, {max(all_ratios):.2%}]")
        
        # 数据质量评估
        print("\n" + "=" * 80)
        print("数据质量评估")
        print("=" * 80)
        
        # 检查数值范围
        if max(all_maxs) <= 1.0:
            print("🔍 数据范围 [0, 1] - 已归一化，建议乘以255转换为8位")
        elif max(all_maxs) <= 255:
            print("🔍 数据范围 [0, 255] - 8位编码，适合直接使用")
        else:
            print("🔍 数据范围超出255 - 需要归一化处理")
        
        # 检查非零像素比例
        min_ratio = min(all_ratios)
        if min_ratio < 0.5:
            print(f"⚠️ 部分深度图非零像素比例过低 ({min_ratio:.2%}) - 可能影响训练效果")
        else:
            print(f"✅ 深度图质量良好，非零像素比例: {min_ratio:.2%} - {max(all_ratios):.2%}")
        
        # 检查动态范围
        avg_std = np.mean(all_stds)
        if avg_std < 10:
            print(f"⚠️ 深度图动态范围较小 (std={avg_std:.1f}) - 可能需要对比度增强")
        else:
            print(f"✅ 深度图动态范围合适 (std={avg_std:.1f})")
    
    return depth_stats


def visualize_depth_sample(dataset, sample_idx=0):
    """
    可视化深度图样本
    
    Args:
        dataset: 数据集对象
        sample_idx (int): 样本索引
    """
    try:
        sample = dataset[sample_idx]
        
        if 'img' in sample and sample['img'].shape[0] == 4:
            # 提取RGB和深度通道
            rgb_channels = sample['img'][:3].numpy().transpose(1, 2, 0)  # CHW -> HWC
            depth_channel = sample['img'][3].numpy()
            
            # 创建可视化
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            # RGB图像
            axes[0].imshow(rgb_channels)
            axes[0].set_title('RGB Image')
            axes[0].axis('off')
            
            # 深度图
            im1 = axes[1].imshow(depth_channel, cmap='viridis')
            axes[1].set_title('Depth Map')
            axes[1].axis('off')
            plt.colorbar(im1, ax=axes[1])
            
            # 深度直方图
            axes[2].hist(depth_channel[depth_channel > 0].flatten(), bins=50, alpha=0.7)
            axes[2].set_title('Depth Histogram (Non-zero)')
            axes[2].set_xlabel('Depth Value')
            axes[2].set_ylabel('Frequency')
            
            plt.tight_layout()
            plt.savefig(f'depth_analysis_sample_{sample_idx}.png', dpi=150, bbox_inches='tight')
            print(f"可视化结果保存为: depth_analysis_sample_{sample_idx}.png")
            
    except Exception as e:
        print(f"❌ 可视化失败: {e}")


if __name__ == '__main__':
    # 分析深度图预处理
    config_path = 'datasets/kitti-stage1.yaml'
    
    if Path(config_path).exists():
        stats = analyze_depth_preprocessing(config_path, num_samples=10)
        
        # 可选：生成可视化
        # try:
        #     from ultralytics.data.rgbd_dataset import YOLORGBDDataset
        #     data_config = yaml_load(config_path)
        #     dataset = YOLORGBDDataset(...)  # 重新创建数据集
        #     visualize_depth_sample(dataset, 0)
        # except:
        #     pass
    else:
        print(f"❌ 配置文件不存在: {config_path}")
        print("请确保数据集配置文件路径正确")
