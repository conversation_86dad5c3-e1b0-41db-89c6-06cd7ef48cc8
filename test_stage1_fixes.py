#!/usr/bin/env python3
"""
测试Stage1修复后的关键功能
"""

import sys
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))


def test_4channel_processing():
    """测试4通道数据处理修复"""
    print("=" * 60)
    print("测试4通道数据处理修复")
    print("=" * 60)
    
    try:
        from ultralytics.data.augment import ToTensor, Format
        
        # 测试ToTensor修复
        print("1. 测试ToTensor 4通道处理...")
        to_tensor = ToTensor()
        
        # 创建4通道测试数据 (H, W, C)
        test_img_4ch = np.random.randint(0, 255, (100, 100, 4), dtype=np.uint8)
        result = to_tensor(test_img_4ch)
        
        print(f"   输入形状: {test_img_4ch.shape}")
        print(f"   输出形状: {result.shape}")
        print(f"   输出数据类型: {result.dtype}")
        print(f"   数值范围: [{result.min():.3f}, {result.max():.3f}]")
        
        # 验证RGB通道是否正确翻转，深度通道是否保持不变
        original_rgb = test_img_4ch[:, :, :3]
        original_depth = test_img_4ch[:, :, 3]
        
        result_rgb = result[:3].permute(1, 2, 0).numpy()
        result_depth = result[3].numpy()
        
        # 检查RGB通道翻转 (BGR -> RGB)
        rgb_flipped_correctly = np.allclose(result_rgb[:, :, 0], original_rgb[:, :, 2] / 255.0, atol=1e-3)
        depth_unchanged = np.allclose(result_depth, original_depth / 255.0, atol=1e-3)
        
        print(f"   ✅ RGB通道翻转正确: {rgb_flipped_correctly}")
        print(f"   ✅ 深度通道保持不变: {depth_unchanged}")
        
        # 测试Format修复
        print("\n2. 测试Format 4通道处理...")
        formatter = Format(bgr=0.0)  # 禁用BGR随机性以便测试
        
        # 创建模拟labels
        labels = {
            'img': test_img_4ch,
            'cls': np.array([0, 1]),
            'instances': type('MockInstances', (), {
                'convert_bbox': lambda self, format: None,
                'denormalize': lambda self, w, h: None,
                'bboxes': np.array([[10, 10, 50, 50], [60, 60, 90, 90]]),
                'segments': [],
                'keypoints': []
            })()
        }
        
        formatted = formatter(labels)
        formatted_img = formatted['img']
        
        print(f"   输入形状: {test_img_4ch.shape}")
        print(f"   输出形状: {formatted_img.shape}")
        print(f"   输出数据类型: {formatted_img.dtype}")
        
        print("✅ 4通道数据处理修复验证通过!")
        return True
        
    except Exception as e:
        print(f"❌ 4通道数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_building():
    """测试模型构建和DepthGetter链接修复"""
    print("\n" + "=" * 60)
    print("测试模型构建和DepthGetter链接修复")
    print("=" * 60)
    
    try:
        from ultralytics.nn.tasks import parse_model
        from ultralytics.utils import yaml_load
        
        # 加载模型配置
        cfg_path = 'ultralytics/cfg/models/v12/yolov12-stage1.yaml'
        cfg = yaml_load(cfg_path)
        
        print(f"1. 加载模型配置: {cfg_path}")
        print(f"   输入通道数: {cfg.get('ch', 'unknown')}")
        print(f"   类别数: {cfg.get('nc', 'unknown')}")
        
        # 构建模型
        print("\n2. 构建模型...")
        model, save = parse_model(cfg, ch=4, verbose=True)  # 4通道输入
        
        print(f"   模型构建成功!")
        print(f"   模型层数: {len(model)}")
        
        # 测试前向传播
        print("\n3. 测试前向传播...")
        test_input = torch.randn(1, 4, 640, 640)
        
        with torch.no_grad():
            output = model(test_input)
        
        print(f"   输入形状: {test_input.shape}")
        if isinstance(output, (list, tuple)):
            print(f"   输出数量: {len(output)}")
            for i, out in enumerate(output):
                print(f"   输出{i}形状: {out.shape}")
        else:
            print(f"   输出形状: {output.shape}")
        
        print("✅ 模型构建和前向传播测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 模型构建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_depth_getter_linking():
    """测试DepthGetter与InputSeparator的链接"""
    print("\n" + "=" * 60)
    print("测试DepthGetter与InputSeparator的链接")
    print("=" * 60)
    
    try:
        from ultralytics.nn.modules.block import InputSeparator, DepthGetter
        
        # 创建InputSeparator
        separator = InputSeparator()
        
        # 创建DepthGetter并手动链接
        depth_getter = DepthGetter(0)
        depth_getter.separator_module = separator
        
        # 测试数据
        test_input = torch.randn(2, 4, 64, 64)
        
        print(f"1. 测试输入形状: {test_input.shape}")
        
        # InputSeparator前向传播
        rgb_output = separator(test_input)
        print(f"2. RGB输出形状: {rgb_output.shape}")
        
        # DepthGetter前向传播
        depth_output = depth_getter(test_input)  # 输入会被忽略
        print(f"3. Depth输出形状: {depth_output.shape}")
        
        # 验证数据正确性
        expected_rgb = test_input[:, :3, :, :]
        expected_depth = test_input[:, 3:4, :, :]
        
        rgb_correct = torch.allclose(rgb_output, expected_rgb)
        depth_correct = torch.allclose(depth_output, expected_depth)
        
        print(f"4. RGB数据正确性: {rgb_correct}")
        print(f"5. Depth数据正确性: {depth_correct}")
        
        if rgb_correct and depth_correct:
            print("✅ DepthGetter链接测试通过!")
            return True
        else:
            print("❌ 数据验证失败!")
            return False
        
    except Exception as e:
        print(f"❌ DepthGetter链接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🔧 Stage1修复验证测试")
    print("=" * 80)
    
    tests = [
        ("4通道数据处理", test_4channel_processing),
        ("模型构建", test_model_building),
        ("DepthGetter链接", test_depth_getter_linking),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有修复验证通过！Stage1已准备就绪。")
    else:
        print("⚠️ 部分测试失败，请检查修复。")
    
    return passed == len(tests)


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
